<!-- 来源分析: @061176c1-dd58-404b-9041-849f92831622 -->

## 基于物理先验的 Transformer 嵌入方案（在线析锂检出）

下面给出把“恒压阶段 I–t 曲线出现平台/凸起”这条物理规律转成可微数学约束并嵌入 Transformer 的最小实现路径，以及适合“在线检出”的架构建议与论文创新点表述。

### 目录与阅读顺序（经校验：合理）
按以下顺序阅读更符合工程实现与复现路径（后续重构章节编号将按照此顺序）：
- [目的与整体逻辑](#goal-logic)
- [输入输出规范](#io-spec)
- [恒压(CV)阶段自动切分与批量评估](#cv-split-batch)
- [自适应门限校准](#adaptive-threshold)
- [最小可运行的 Python 代码骨架](#min-python-skeleton)
- [基于“回嵌强度 + 小电流权重”的软指标与风险评分](#soft-indicator-risk)
- [物理特征的可微数学刻画](#phys-math)
- [按圈聚合与 GMM 二值化](#per-cycle-gmm)
- [可视化示例（matplotlib）](#viz-matplotlib)
- [三个嵌入点（条件 token / 物理注意力偏置 / 物理一致性损失）](#three-injections)
- [适用于“在线检出”的 Transformer 架构](#online-transformer)
- [论文“创新点”推荐写法](#paper-innovation)
- [参数与工程建议（在线友好）](#params-engineering)

---

<a id="phys-math"></a>
### 1. 物理特征的可微数学刻画
（支撑—实现）支撑“适用于在线检出的 Transformer 架构”与“基于回嵌强度 + 小电流权重的风险评分”（提供 S_plat/S_bump/S_mono 的可微定义），支撑“最小可运行的 Python 代码骨架”的计算公式与“按圈聚合与 GMM 二值化”的段内特征来源；通过“平滑+导数”“单调基线+软门”将平台/凸起/非单调转为可学习的可微量。
以归一化时间 \(s\in[0,1]\) 和电流 \(i(s)\) 为例（对 I–t 做高斯/SG 可微平滑后计算差分）：
- 一阶导与二阶导（用可微卷积近似）：
  $$
  i'(s)=\frac{\mathrm{d}i}{\mathrm{d}s},\quad i''(s)=\frac{\mathrm{d}^2 i}{\mathrm{d}s^2}
  $$
- 平台软指标（低斜率、持续性）：
  - 局部平坦度：
    $$
    p(s)=\sigma\!\left(\frac{\tau-|i'(s)|}{\sigma_p}\right)
    $$
  - 平台分数（可加软连通性）：
    $$
    S_{\text{plat}}=\frac{1}{N}\sum_s p(s)
    $$
- 凸起软指标（先正曲率后负曲率的“包络”）：
  - 正、负曲率面积：
    $$
    A_+=\sum_s \mathrm{ReLU}(i''(s)-\kappa_+),\qquad
    A_-=\sum_s \mathrm{ReLU}(-\kappa_- - i''(s))
    $$
  - 凸起分数：
    $$
    S_{\text{bump}}=\sigma\!\left(\frac{A_+\cdot A_- - \eta}{\sigma_b}\right)
    $$
- 非单调性（正常应单调下降）：
  $$
  S_{\text{mono}}=\frac{1}{N}\sum_s \mathrm{ReLU}(i'(s))
  $$

得到物理软标签/打分：
$$
y_{\text{phys}}=\sigma\!\left(\alpha_1 S_{\text{plat}}+\alpha_2 S_{\text{bump}}+\alpha_3 S_{\text{mono}}+b\right)
$$

以上全是可微的，对模型参数可反传（平滑与差分用 1D conv 即可）。

---

<a id="three-injections"></a>
### 2. 三个嵌入点（推荐都用，代价低、增益互补）
（支撑—实现）支撑“适用于在线检出的 Transformer 架构”的物理注入路径、支撑“最小可运行的 Python 代码骨架”的一致性蒸馏与“论文创新点”写作；通过“条件 token（先验输入）+ 物理注意力偏置 B（结构归纳）+ 物理一致性损失（目标约束）”三层嵌入促进弱/无标签场景下对物理显著片段的自对齐。
- 作为“条件token”输入（Unisolver式）
  - 把 \(\{S_{\text{plat}},S_{\text{bump}},S_{\text{mono}}\}\) 及其多尺度版本、平台段起止的软位置编码等，编码成若干条件 token，与时间序列 token 一起送入编码器/交叉注意力。在线代价极低。
- 作为“注意力物理偏置”（Physics-Attention）
  - 为任意时刻对 \(i,j\) 构造可学习加性偏置：
    $$
    B_{ij}=\beta_1\, p(i)\,p(j)+\beta_2 \exp\!\left(-\frac{(i''(i)-i''(j))^2}{2\sigma_\kappa^2}\right)
    $$
  - 在注意力打分中用：
    $$
    \mathrm{Attn}(Q,K,V)=\mathrm{softmax}\!\left(\frac{QK^\top}{\sqrt{d}}+B\right)\,V
    $$
  - 物理平台/曲率相似的时间点天然更“互注意”，利于弱监督下自组织出平台/凸起模式。
- 作为“物理一致性损失”（PINNs式但不解 PDE）
  - 一致性蒸馏：
    $$
    \mathcal{L}_{\text{phys}}=\mathrm{BCE}\big(y_{\text{pred}},\,y_{\text{phys}}\big)
    $$
  - 排序/相关（目标）：
    $$
    \max\ \mathrm{corr}\!\left(y_{\text{logit}},\,S_{\text{plat}}+\lambda S_{\text{bump}}\right)
    $$
  - 可选辅助头预测软平台段起止、凸起强度，加入 L2/IoU 式损失

---

<a id="online-transformer"></a>
### 3. 适用于“在线检出”的 Transformer 架构
（支撑—实现）支撑端到端在线告警输出并承接“物理特征的可微数学刻画/三个嵌入点”的特征与约束，向“按圈聚合与 GMM 二值化”提供圈级标注入口、向“可视化示例”提供工程化支撑；通过流式因果注意力、交叉注意力条件化、物理偏置与多目标损失实现低延迟、可解释的在线检出。
- 核心：流式因果 Transformer（滑窗 W≈5–30 s，步长 0.1–0.5 s），相对位置编码
- 低延迟注意力：线性注意力（Performer/Linear Transformer）或局部+稀疏全局混合
- 物理增强：
  - 交叉注意力接入“物理条件 token”
  - 自注意力加上“物理偏置 \(B\)”（上式）
  - 分类头输出析锂风险 \(y_{\text{pred}}\)，辅助头输出平台/凸起参数
- 训练损失：
  $$
  \mathcal{L}=\mathcal{L}_{\text{cls}}+\lambda_1\,\mathcal{L}_{\text{phys}}+\lambda_2\,\mathcal{L}_{\text{aux}}
  $$
- 推理：仅用最近一窗数据，边采边算，延迟<窗口步长；CV 阶段由电压阈值/方差门控触发

简要实现要点（PyTorch 注意力偏置注入示例）：
```python
# logits = (Q @ K.T)/sqrt(d) + B
attn_scores = torch.einsum('btd,bsd->bts', Q, K) / math.sqrt(d) + B
attn = torch.softmax(attn_scores, dim=-1) @ V
```

---

<a id="per-cycle-gmm"></a>
### 4. 按圈聚合与 GMM 二值化（从 y_soft 自动得到 y∈{0,1}）
（总结）从单圈数据自动提取 y（0/1）：
- aggregate_cycle_features：每圈聚合 Rcv（CV 软风险）、CE、Pdv（简易 dQ/dV 峰）、S_amp_max、Q_tilde_max，并给出未融合 ΔCE 的 y_soft；
- aggregate_cycles_and_binarize：计算 CE 基线→融合 ΔCE 得最终 y_soft→用 GMM（退化到 Otsu）自动二值化，产出 y 与解释项。
支撑：
- “适用于在线检出的 Transformer 架构”：提供圈级 y 与可解释指标（用于告警与记录）；
- “最小可运行的 Python 代码骨架”：复用段内特征，形成圈级软/硬标签（y_soft, y）；
- “目的与整体逻辑”：贯彻“规则零样本→软标签→弱监督校准”的落地路径；
- “可视化示例”：核查高 y_soft 圈并优化参数。

```python
import numpy as np

try:
    from sklearn.mixture import GaussianMixture
    _HAS_SK = True
except Exception:
    _HAS_SK = False

def cycle_charge_discharge_Q(t, Q):
    """基于 dQ/dt 的正负号分段，粗略估计单圈的充/放电容量"""
    t = np.asarray(t).astype(float)
    Q = np.asarray(Q).astype(float)
    dQ = np.gradient(Q, t)
    Qchg = float(np.sum(np.maximum(dQ, 0.0) * np.maximum(np.gradient(t), 0)))  # 正向积分
    Qdis = float(np.sum(np.maximum(-dQ, 0.0) * np.maximum(np.gradient(t), 0))) # 负向积分取正
    return Qchg, Qdis

def dq_dv_peak(V, Q, tail_ratio=0.1):
    """
    简易 dQ/dV 峰值：取电压尾段(末尾一定比例)区域，计算 |dQ/dV| 的最大值作为峰值代理
    注：若仅有全电池电压，不能严格对应 0V vs Li/Li+，此处仅用作异常度参考
    """
    V = np.asarray(V).astype(float)
    Q = np.asarray(Q).astype(float)
    dQdV = np.gradient(Q) / (np.gradient(V) + 1e-12)
    n = len(V)
    tail_n = max(5, int(n * tail_ratio))
    tail_abs = np.abs(dQdV[-tail_n:])
    return float(np.nanmax(tail_abs))

def aggregate_cycle_features(t, I, V, Q, params=None):
    """针对单圈数据，聚合 (CE/ΔCE、Rcv、Pdv、解释性指标) 与 y_soft"""
    if params is None:
        params = {}

    # 1) 恒压段指标：取自适应切分的所有 CV 段，聚合为 Rcv 与关键解释
    cv_results = eval_cv_segments_adaptive(t, I, V, Q, params=params)
    if len(cv_results) == 0:
        Rcv = 0.0
        S_amp_max = 0.0
        Q_tilde_max = 0.0
    else:
        y_list = [r['y_risk'] for r in cv_results]
        Rcv = float(np.max(y_list))  # 也可用均值/加权
        S_amp_max = float(np.max([r['S_amp'] for r in cv_results]))
        Q_tilde_max = float(np.max([r['Q_tilde'] for r in cv_results]))

    # 2) 库伦效率（粗略）：按 dQ/dt 正负号积分
    Qchg, Qdis = cycle_charge_discharge_Q(t, Q)
    CE = float(Qdis / (Qchg + 1e-12)) if Qchg > 1e-12 else np.nan
    # 需要健康基线 CE0 进行 ΔCE；此处留空，由上层批量时计算

    # 3) dQ/dV 简易峰值（尾段）
    Pdv = dq_dv_peak(V, Q)

    # 4) 软融合（先不含 ΔCE，由上层补齐）
    betas = params.get('betas_cycle', [0.0, 1.0, 0.5, 0.5])  # [β_CE, β_Rcv, β_Pdv, β_cons]
    b0 = params.get('bias_cycle', 0.0)
    Cons = S_amp_max + Q_tilde_max  # 一致性与幅度的组合
    z = (betas[1]*Rcv + betas[2]*Pdv + betas[3]*Cons + b0)
    y_soft = 1.0 / (1.0 + np.exp(-z))

    return {
        'Rcv': Rcv,
        'S_amp_max': S_amp_max,
        'Q_tilde_max': Q_tilde_max,
        'Pdv': Pdv,
        'CE': CE,
        'y_soft_wo_dCE': float(y_soft),  # 未融合 ΔCE 的软分
    }

def binarize_ysoft(ysoft_arr, method='gmm'):
    """将 y_soft 转为 y∈{0,1}；优先用 GMM，退化到 Otsu/分位阈值"""
    ysoft_arr = np.asarray(ysoft_arr).reshape(-1, 1)
    if method == 'gmm' and _HAS_SK and len(ysoft_arr) >= 10:
        gm = GaussianMixture(n_components=2, covariance_type='full', random_state=0)
        gm.fit(ysoft_arr)
        probs = gm.predict_proba(ysoft_arr)
        # 假设第1列为“低风险类”，以均值判断
        means = gm.means_.flatten()
        high_idx = int(np.argmax(means))
        y = (probs[:, high_idx] > 0.5).astype(int)
        return y, {'means': means.tolist()}
    # 退化到 Otsu: 用密度网格近似寻找最佳阈值
    xs = ysoft_arr.flatten()
    bins = np.linspace(0, 1, 101)
    hist, edges = np.histogram(xs, bins=bins)
    hist = hist.astype(float) / max(1, hist.sum())
    cumsum = np.cumsum(hist)
    cumsum_mean = np.cumsum(hist * ((edges[:-1] + edges[1:]) / 2))
    global_mean = np.sum(hist * ((edges[:-1] + edges[1:]) / 2))
    # 类间方差
    numer = (global_mean * cumsum - cumsum_mean) ** 2
    denom = (cumsum * (1 - cumsum) + 1e-12)
    var_between = numer / denom
    th_idx = int(np.nanargmax(var_between))
    th = float((edges[th_idx] + edges[th_idx+1]) / 2)
    y = (xs >= th).astype(int)
    return y, {'threshold': th}

def aggregate_cycles_and_binarize(cycles, ce_baseline=None, params=None):
    """
    输入 cycles: 列表，每个元素为 {'t':..., 'I':..., 'V':..., 'Q':...}
    可选 ce_baseline: 若提供(如前 N0 圈中位数)，将融合 ΔCE
    返回：每圈的特征、y_soft、二值 y 以及解释项
    """
    feats = []
    for c in cycles:
        f = aggregate_cycle_features(c['t'], c['I'], c['V'], c['Q'], params=params)
        feats.append(f)
    # 计算 ΔCE 基线
    CE_vals = np.array([f['CE'] for f in feats if not np.isnan(f['CE'])])
    CE0 = float(np.nanmedian(CE_vals[:max(1, int(len(CE_vals)*0.1))])) if ce_baseline is None else ce_baseline
    ysoft = []
    for f in feats:
        dCE = 0.0 if np.isnan(f['CE']) else max(0.0, CE0 - f['CE'])
        betas = (params or {}).get('betas_cycle', [0.5, 1.0, 0.5, 0.5])
        b0 = (params or {}).get('bias_cycle', 0.0)
        Cons = f['S_amp_max'] + f['Q_tilde_max']
        z = (betas[0]*dCE + betas[1]*f['Rcv'] + betas[2]*f['Pdv'] + betas[3]*Cons + b0)
        ysoft.append(1.0 / (1.0 + np.exp(-z)))
        f['dCE'] = float(dCE)
        f['y_soft'] = float(ysoft[-1])
        f['CE0'] = float(CE0)
    ysoft = np.array(ysoft)
    y, info = binarize_ysoft(ysoft, method='gmm')
    for i, f in enumerate(feats):
        f['y'] = int(y[i])
    return feats, {'CE0': CE0, 'binarize_info': info}

# 用法（示例）：
# cycles = [ {'t':t1, 'I':I1, 'V':V1, 'Q':Q1}, {'t':t2, 'I':I2, ...}, ... ]
# feats, extra = aggregate_cycles_and_binarize(cycles)
# for i, f in enumerate(feats):
#     print(i+1, f['y'], f['y_soft'], f['dCE'], f['Rcv'], f['S_amp_max'], f['Q_tilde_max'], f['Pdv'])
```
<a id="paper-innovation"></a>
### 11. 论文“创新点”推荐写法
（支撑—实现）支撑整篇写作结构化呈现：将第1/6节的物理先验指标、第2节嵌入策略与第3节在线架构凝练为论文卖点；通过“物理偏置 + 软弱式一致性 + 条件 token”的三位一体创新路径，强调可解释与工程落地。
- 物理注意力偏置（Physics-Attention for LP）
  - 首次把“恒压 I–t 平台/凸起的微分几何先验”（低斜率、先正后负曲率）显式转化为注意力加性偏置，促使模型在无/弱标注下自对齐物理显著片段，适配在线流式检测。
- 软弱式物理一致性（Weak-form Physics Consistency）
  - 以可微的“平台/凸起/非单调”软指标构造物理一致性蒸馏损失，避免昂贵的PDE残差，鲁棒且部署友好。
- 条件化物理 token 融合
  - 多尺度平台/曲率 token 与时序 token 的交叉注意力融合，提升早期微弱析锂征象的可分性与可解释性（输出平台概率与起止）。

---

<a id="params-engineering"></a>
### 12. 参数与工程建议（在线友好）
（支撑—实现）为第3/6/7/8/9/11/4节的工程实现提供参数与实践指导：
- 平滑/导数核、单调基线 α、曲率/斜率阈值与分位自适应配置；
- 多尺度与量化部署要点；
- 解释输出与质检流程与门限校准建议。
- 平滑核宽度随采样率自适应；\(\tau,\kappa_+,\kappa_-,\eta\) 初值用健康样本分位数设定，训练为可学习标量
- 噪声鲁棒：一阶/二阶导用可微 Savitzky–Golay 或固定 1D 卷积核；加 Huber/TV 正则
- 只在 CV 窗口启用物理损失与偏置；非 CV 阶段正常因果注意力
- 资源：单卡可达亚百毫秒/步；量化 INT8 不影响物理偏置加法

如需，我可以基于上述公式给出一版最小可运行的 PyTorch 训练骨架（含滑窗、物理 token、注意力偏置与损失），方便你直接做在线推理测试。

---

<a id="io-spec"></a>
### 13. 输入输出规范（接口与数据格式）

（目的）明确在线/离线场景的输入、输出、异常与配置要求，保证跨团队/跨系统可用与可复现。

- 输入数据（序列必填）
  - t: 时间（单位 s），严格递增；
  - I: 电流（单位 A）；
  - V: 电压（单位 V）；
  - Q: 容量（单位 Ah），同长度；
  - 可选元数据: cell_id, cycle_id, T(°C), C_rate, V_cv（便于分层评估/自适应）。

- 前置与分段
  - 采样率: 建议 Δt ≤ 1 s（或每窗≥100点）；
  - CV 判定: 参见第8/9节固定/自适应两种方式；
  - 同圈多 CV 段: 默认分别评估再取 max/mean（可配置）。

- 在线推理（滑窗接口）
  - 签名（Python）:
    ```python
    def predict_window(t, I, V, Q, meta=None) -> dict
    ```
  - 输入: 最近一窗数据（W 秒，步长 H 秒），电压进入 CV 后启动；
  - 输出字段（窗口级）:
    - y_risk: float, 0~1;
    - segment: [start_idx, end_idx]（相对窗内）;
    - S_plat, S_bump, S_mono, S_amp, Q_tilde, C_low, C_kappa: float;
    - thresholds: {v_var_th, i_slope_th, min_len}（若启用自适应切分）;
    - level: info/warn/crit（可选告警级别，映射自 y_risk）。
  - 示例（JSON）:
    ```json
    {
      "y_risk": 0.83,
      "segment": [120, 380],
      "S_plat": 0.71,
      "S_bump": 0.62,
      "S_mono": 0.03,
      "S_amp": 0.012,
      "Q_tilde": 0.004,
      "C_low": 0.78,
      "C_kappa": 0.65,
      "thresholds": {"v_var_th": 1e-4, "i_slope_th": -1e-5, "min_len": 50},
      "level": "warn"
    }
    ```

- 离线评估（圈级接口）
  - 签名（Python）:
    ```python
    def aggregate_cycles_and_binarize(cycles, ce_baseline=None, params=None) -> (list, dict)
    ```
  - 输入: cycles 为列表，每个元素包含单圈的 t,I,V,Q；
  - 输出（list 中每圈的 dict）:
    - y: int, {0,1}; y_soft: float；dCE, CE0, CE；
    - Rcv, S_amp_max, Q_tilde_max, Pdv；
    - segments: 可选，圈内 CV 段列表（若需要可在函数中保留）；
  - 汇总信息（dict）:
    - {CE0, binarize_info}，含 GMM/Otsu 阈值或均值等信息。
  - CSV/JSON 示例（每圈一行/对象）:
    ```csv
    cell_id,cycle_id,y,y_soft,CE,CE0,dCE,Rcv,S_amp_max,Q_tilde_max,Pdv
    A01,12,1,0.87,0.992,0.997,0.005,0.81,0.015,0.006,2.3
    ```

- REST API（可选）
  - POST /predict_window
    - body: {t:[...], I:[...], V:[...], Q:[...], meta:{cell_id,...}}
    - resp: 同上窗口级 JSON；
  - POST /aggregate_cycles
    - body: {cycles:[{t,I,V,Q,meta}, ...]}
    - resp: {rows:[每圈输出...], summary:{CE0,...}}

- 异常与退化
  - 无 CV 段/片段过短/噪声过高: 返回 status: degraded，并仅输出统计项（如 S_plat 均值），不触发 y/y_risk；
  - 单位/采样异常: 返回 status: invalid，附带 reason；
  - 最小可用: 每窗≥20点（或≥5 s）且进入 CV 状态。

- 阈值与校准
  - y 二值阈值: 默认 GMM；无 sklearn 时退化到 Otsu/分位（q≥0.9）；
  - CE0: 默认前 N0（如 10%）圈中位数；可指定 ce_baseline；
  - 告警级别: level=warn/crit 的分位映射（如 y_risk ≥ 0.8/0.9）。

- 质量指标
  - 线上: 触发率、重复告警压缩率、确认时延；
  - 离线: ROC/AUC、PR、误警/漏检率、与容量/CE趋势的相关性。

- 配置与版本
  - params（YAML/JSON）: sg_win, tau_slope, sigma_p, kappa_pos/neg, sigma_b, eta, sigma_i, alpha_base, k_scale, betas, bias 等；
  - 切分/自适应参数: v_var_win, v_q, slope_win, slope_q, min_sec；
  - 版本化: config_version, model_version, calibration_version。

<a id="soft-indicator-risk"></a>
### 6. 基于“回嵌强度 + 小电流权重”的软指标与风险评分（更精准、更周到）
（总结）以 δi+ 与 w_low 量化“回嵌越强+多见于小电流”的物理先验，组合 S_amp、\(\tilde Q_{Li}\)、C_low、C_\kappa 与 S_plat/S_bump/S_mono 融合为 y_risk；支撑第3节在线检出的主风险信号、第4节圈级软/硬标签的核心特征、第9节可视化与第12节参数标定。

为贴合“回嵌越强（电流凸起越大）→ 对应锂量越多”与“回嵌一般出现在小电流下”的事实，在不增加实现复杂度的前提下，引入两类可微软指标并纳入风险评分：

- 基线电流（无回嵌的期望衰减）与正偏差
  - 用指数/单调平滑得到基线 \(i_{\text{base}}(s)\)，如：
    $$
    i_{\text{base}}(s)\approx a\,e^{-s/\tau_b}+b\quad \text{或单调回归/指数滑动平均}
    $$
  - 正偏差（回嵌“强度”）：
    $$
    \delta i^+(s)=\mathrm{ReLU}\big(i(s)-i_{\text{base}}(s)\big)
    $$

- 小电流权重（强调低电流区域的回嵌更可信）
  - 软权重：
    $$
    w_{\text{low}}(s)=\sigma\!\left(\frac{i_{\text{thr}}-|i(s)|}{\sigma_i}\right),\quad i_{\text{thr}}\in(0, I_0)
    $$
    其中 \(I_0\) 为 CV 初始电流；\(i_{\text{thr}}\) 可取 \(I_0\) 的 5%–20% 分位，\(\sigma_i\) 为温和平滑因子。

- 回嵌幅度软指标（无量纲、可对比）
  $$
  S_{\text{amp}}=\frac{1}{N}\sum_s w_{\text{low}}(s)\,\delta i^+(s)
  $$

- 回嵌锂量代理（与实际锂量单调相关的软积分）
  - 连续时间：
    $$
    \hat Q_{\text{Li}}=k\int w_{\text{low}}(t)\,\delta i^+(t)\,\mathrm{d}t
    $$
  - 离散时间：
    $$
    \hat Q_{\text{Li}}=k\sum_t w_{\text{low}}(t)\,\delta i^+(t)\,\Delta t
    $$
  - 归一化（便于跨循环/跨电芯比较）：
    $$
    \tilde Q_{\text{Li}}=\frac{\hat Q_{\text{Li}}}{Q_{\text{CV}}}\quad \text{或}\quad \frac{\hat Q_{\text{Li}}}{I_0\,T_{\text{CV}}}
    $$

- 发生位置与一致性（辅助可信度）
  - 低电流共现度：
    $$
    C_{\text{low}}=\frac{\sum_s w_{\text{low}}(s)\,\mathbb{1}\{\delta i^+(s)>0\}}{\sum_s \mathbb{1}\{\delta i^+(s)>0\}+\varepsilon}
    $$
  - 与曲率一致性（凸起而非随机噪声）：
    $$
    C_{\kappa}=\frac{\sum_s \mathbb{1}\{i''(s)>\kappa_+\}\,\mathbb{1}\{\delta i^+(s)>0\}}{\sum_s \mathbb{1}\{\delta i^+(s)>0\}+\varepsilon}
    $$

- 融合后的风险评分（软风险，不依赖硬阈值）
  $$
  y_{\text{risk}}=\sigma\!\Big(\beta_1 S_{\text{bump}}+\beta_2 S_{\text{plat}}+\beta_3 S_{\text{mono}}+\beta_4 S_{\text{amp}}+\beta_5 \tilde Q_{\text{Li}}+\beta_6 C_{\text{low}}+\beta_7 C_{\kappa}+b\Big)
  $$

实现与标定建议（更准确、更周到）

- **基线估计稳健化**：对 \(i(s)\) 先做 SG/高斯平滑；\(i_{\text{base}}\) 用指数衰减拟合 + 单调性正则或等凸回归，避免把噪声当作回嵌。
- **门限自适应**：\(i_{\text{thr}}\) 取健康周期/早期周期的分位自适应；\(k\) 通过少量标注（或文献估计）跨电芯校准。
- **多尺度一致性**：在数个时间尺度（短/中/长窗）重复计算 \(S_{\text{amp}},\tilde Q_{\text{Li}}\)，取加权和，提高对不同回嵌时程的灵敏度。
- **解释输出**：同时输出 \(\tilde Q_{\text{Li}}\)、回嵌起止软位置、\(C_{\text{low}},C_{\kappa}\) 作为风险解释，便于工程确认。
- **与既有指标兼容**：保留 \(S_{\text{bump}},S_{\text{plat}},S_{\text{mono}}\)；新引入的 \(S_{\text{amp}}\) 与 \(\tilde Q_{\text{Li}}\) 直接体现“电流越大→锂量越多、且偏向小电流出现”的规律。

---

<a id="min-python-skeleton"></a>
### 7. 最小可运行的 Python 代码骨架（仅 I, V, Q, t）
（总结）给出纯 I,V,Q,t 的最小实现，输出段内软指标与 y_risk；支撑：第6节指标计算的参考实现、第8/9节批/自适应切分后的段内评估内核、第4节圈级聚合输入；实现：平滑/导数、单调基线、软门与可复用 numpy 函数。

```python
# ========== 核心功能：单个 CV 段软指标 + 融合风险评分 y_risk ==========
# 仅依赖 I, V, Q, t 四列数据；输入需已切到单个恒压(CV)阶段
import numpy as np

# Sigmoid 函数（用于软门与风险映射）
def sigmoid(x):
    return 1.0 / (1.0 + np.exp(-x))

# 简单滑动平均平滑（近似 SG/高斯，在线低成本）
def smooth_ma(x, win=11):
    win = max(3, int(win) // 2 * 2 + 1)  # odd
    pad = win // 2
    x_pad = np.pad(x, (pad, pad), mode='reflect')
    ker = np.ones(win) / win
    y = np.convolve(x_pad, ker, mode='valid')
    return y

# 对归一化时间 s 计算一阶/二阶导数
def derivatives(x, s):
    dx = np.gradient(x, s)
    d2x = np.gradient(dx, s)
    return dx, d2x

# 单调非增的基线估计：近似指数衰减，并避免把噪声当作回嵌
def monotone_baseline(i_smooth, alpha=0.98):
    """简单单调基线：EWMA 并强制非增，逼近指数衰减基线"""
    base = np.empty_like(i_smooth)
    base[0] = i_smooth[0]
    for k in range(1, len(i_smooth)):
        ema = alpha * base[k-1] + (1 - alpha) * i_smooth[k]
        base[k] = min(base[k-1], ema)
    return base

def soft_metrics_from_IVQt(t, I, V, Q, params=None):
    """
    输入: t(s), I(A), V(V), Q(Ah)
    输出: 软指标与 y_risk
    假设已切到单个 CV 段（电压近似恒定，电流整体下降）
    """
    if params is None:
        params = {}
    # 超参（可按健康样本分位自适应）
    p = {
        'sg_win': params.get('sg_win', 11),
        'tau_slope': params.get('tau_slope', 0.002),
        'sigma_p': params.get('sigma_p', 0.001),
        'kappa_pos': params.get('kappa_pos', 0.0005),
        'kappa_neg': params.get('kappa_neg', 0.0005),
        'sigma_b': params.get('sigma_b', 0.05),
        'eta': params.get('eta', 0.0),
        'sigma_i': params.get('sigma_i', 0.05),
        'alpha_base': params.get('alpha_base', 0.98),
        'k_scale': params.get('k_scale', 1.0),
        'betas': params.get('betas', [1.0, 1.0, 0.5, 1.0, 1.0, 0.5, 0.5]),
        'bias': params.get('bias', 0.0),
    }

    t = np.asarray(t).astype(float)
    I = np.asarray(I).astype(float)
    V = np.asarray(V).astype(float)
    Q = np.asarray(Q).astype(float)
    N = len(t)
    assert N > 5

    # 归一化时间 s ∈ [0,1]
    s = (t - t[0]) / max(1e-12, (t[-1] - t[0]))

    # 平滑与导数（降低噪声对导数与曲率的影响）
    I_s = smooth_ma(I, p['sg_win'])
    dI, d2I = derivatives(I_s, s)

    # 平台度：斜率越小越平，用 sigmoid 软门计算占比
    p_s = sigmoid((p['tau_slope'] - np.abs(dI)) / max(1e-12, p['sigma_p']))
    S_plat = float(np.mean(p_s))

    # 凸起度：曲率先正后负的面积乘积 → 软强度
    A_plus = np.sum(np.maximum(d2I - p['kappa_pos'], 0.0)) / N
    A_minus = np.sum(np.maximum(-(p['kappa_neg'] + d2I), 0.0)) / N
    S_bump = float(sigmoid(((A_plus * A_minus) - p['eta']) / max(1e-12, p['sigma_b'])))

    # 非单调性：上升斜率的平均值（越大越异常）
    S_mono = float(np.mean(np.maximum(dI, 0.0)))

    # 基线 + 正偏差：回嵌强度 δi+ = max(I - I_base, 0)
    I_base = monotone_baseline(I_s, alpha=p['alpha_base'])
    delta_pos = np.maximum(I_s - I_base, 0.0)

    # 小电流权重：回嵌通常发生在小电流末期
    I0 = np.abs(I_s[0])
    i_thr = 0.1 * I0  # 可替换为分位自适应
    w_low = sigmoid((i_thr - np.abs(I_s)) / max(1e-12, p['sigma_i']))

    # 回嵌幅度指标：S_amp = 均值(w_low * δi+)
    S_amp = float(np.mean(w_low * delta_pos))

    # 软回嵌锂量代理（离散）：近似与实际锂量单调相关
    dt = np.gradient(t)
    Q_hat = float(p['k_scale'] * np.sum(w_low * delta_pos * dt))
    Q_cv = max(1e-12, float(Q[-1] - Q[0]))
    Q_tilde = float(Q_hat / Q_cv)

    # 一致性系数：在低电流与正曲率区域的共现比例
    mask_pos = (delta_pos > 0)
    C_low = float(np.sum((w_low > 0.5) & mask_pos) / max(1, np.sum(mask_pos)))
    C_kappa = float(np.sum((d2I > p['kappa_pos']) & mask_pos) / max(1, np.sum(mask_pos)))

    # 融合软风险：无硬阈值，输出 0~1 概率
    b1, b2, b3, b4, b5, b6, b7 = p['betas']
    z = (b1*S_bump + b2*S_plat + b3*S_mono + b4*S_amp + b5*Q_tilde + b6*C_low + b7*C_kappa + p['bias'])
    y_risk = float(sigmoid(z))

    return {
        'S_plat': S_plat, 'S_bump': S_bump, 'S_mono': S_mono,
        'S_amp': S_amp, 'Q_tilde': Q_tilde, 'C_low': C_low, 'C_kappa': C_kappa,
        'y_risk': y_risk,
        'debug': {
            's': s, 'I_s': I_s, 'I_base': I_base, 'dI': dI, 'd2I': d2I,
            'delta_pos': delta_pos, 'w_low': w_low,
        }
    }

# 用法示例（I,V,Q,t 为 numpy 数组，已切到单个 CV 段）
# metrics = soft_metrics_from_IVQt(t, I, V, Q)
# print(metrics['y_risk'], metrics['S_amp'], metrics['Q_tilde'])
```

---

<a id="cv-split-batch"></a>
### 8. 恒压(CV)阶段自动切分与批量评估
（总结）提供 CV 段自动定位，支撑第1节只在恒压段计算物理软指标、第7节段内 y_risk 评估窗口、第4节圈级聚合的段清单与第11节可视化样本选择；实现：电压滑动方差 + 电流局部斜率门控并合并连续区间。

```python
# ========== CV 段自动定位与批量评估：低方差电压 + 下降电流斜率 ==========
import numpy as np

def find_cv_segments(t, V, I, v_var_win=51, v_var_th=1e-4, i_slope_th=-1e-5, min_len=50):
    """
    基于启发式的恒压阶段定位：
    (1) 电压在窗口内的方差较低 → 近似恒定
    (2) 电流在窗口内的线性斜率为负 → 整体下降
    返回满足条件的连续段 (start_idx, end_idx)
    参数：
      - v_var_win: 电压滑动方差窗口长度
      - v_var_th: 低方差阈值
      - i_slope_th: 负斜率阈值（更负更严格）
      - min_len: 最小段长度（样本数）
    """
    t = np.asarray(t).astype(float)
    V = np.asarray(V).astype(float)
    I = np.asarray(I).astype(float)
    N = len(t)
    # 电压滑动方差
    half = max(1, v_var_win // 2)
    V_pad = np.pad(V, (half, half), mode='edge')
    var_arr = np.empty(N)
    for k in range(N):
        seg = V_pad[k:k+2*half+1]
        var_arr[k] = np.var(seg)
    # 简单门控: 低方差为候选
    mask_v = var_arr < v_var_th
    # 电流斜率(线性拟合局部斜率)
    I_pad = np.pad(I, (half, half), mode='edge')
    slope = np.empty(N)
    for k in range(N):
        segI = I_pad[k:k+2*half+1]
        segt = np.linspace(-half, half, 2*half+1)
        # 最小二乘直线斜率
        A = np.vstack([segt, np.ones_like(segt)]).T
        m, _ = np.linalg.lstsq(A, segI, rcond=None)[0]
        slope[k] = m
    mask_i = slope < i_slope_th
    mask = mask_v & mask_i
    # 合并连续 True 段，得到候选 CV 段
    segs = []
    start = None
    for idx, flag in enumerate(mask):
        if flag and start is None:
            start = idx
        if (not flag or idx == N-1) and start is not None:
            end = idx if flag is False else idx
            if end - start + 1 >= min_len:
                segs.append((start, end))
            start = None
    return segs

def eval_cv_segments(t, I, V, Q, params=None):
    """对自动切分出的每个 CV 段计算软指标与 y_risk（批量评估）"""
    segs = find_cv_segments(t, V, I)
    results = []
    for s, e in segs:
        t_seg, I_seg, V_seg, Q_seg = t[s:e+1], I[s:e+1], V[s:e+1], Q[s:e+1]
        metrics = soft_metrics_from_IVQt(t_seg, I_seg, V_seg, Q_seg, params=params)
        metrics['segment'] = (int(s), int(e))
        results.append(metrics)
    return results

# 用法（示例）：
# cv_results = eval_cv_segments(t_all, I_all, V_all, Q_all)
# for r in cv_results:  # 遍历每个 CV 段
#     print(r['segment'], r['y_risk'], r['S_amp'], r['Q_tilde'])
```

---

<a id="adaptive-threshold"></a>
### 9. 自适应门限校准（基于健康历史分位）
（总结）从健康历史分位自适应估计 \(v_{var\_th}\)/\(i_{slope\_th}\)/\(min\_len\) 以稳健定位 CV 段；支撑第6/7节在多工况下的稳定评估、第8节替换固定阈值的切分流程、第11节可视化核查与第4节跨圈/跨电芯的一致性。
（总结）从健康历史分位自适应估计 \(v_{var\_th}\)/\(i_{slope\_th}\)/\(min\_len\) 以稳健定位 CV 段；支撑第6/7节在多工况下的稳定评估、第8节替换固定阈值的切分流程、第11节可视化核查与第4节跨圈/跨电芯的一致性。

```python
# ========== 自适应门限校准：从(健康/正常)历史分位自动估计阈值 ==========
import numpy as np

def _rolling_var(x, win=51):
    half = max(1, win // 2)
    x_pad = np.pad(x, (half, half), mode='edge')
    var_arr = np.empty(len(x))
    for k in range(len(x)):
        seg = x_pad[k:k+2*half+1]
        var_arr[k] = np.var(seg)
    return var_arr

def _local_slope(x, win=51):
    half = max(1, win // 2)
    x_pad = np.pad(x, (half, half), mode='edge')
    slope = np.empty(len(x))
    segt = np.linspace(-half, half, 2*half+1)
    A = np.vstack([segt, np.ones_like(segt)]).T
    for k in range(len(x)):
        segx = x_pad[k:k+2*half+1]
        m, _ = np.linalg.lstsq(A, segx, rcond=None)[0]
        slope[k] = m
    return slope

def calibrate_cv_thresholds(t, V, I, v_var_win=51, v_q=0.1, slope_win=51, slope_q=0.2, min_sec=30.0):
    """
    自适应估计 CV 切分门限（用于不同倍率/温度/设备的鲁棒切分）：
      - v_q: 电压方差的分位(0.05~0.2)，越小→方差越低→更严格
      - slope_q: 负斜率分位(仅在 slope<0 上取分位；0.1 表示更负)，越小→更严格
      - min_sec: 段最短持续时间（秒）
    返回: v_var_th(电压低方差阈值), i_slope_th(负斜率阈值), min_len(最小样本数)
    """
    t = np.asarray(t).astype(float)
    V = np.asarray(V).astype(float)
    I = np.asarray(I).astype(float)
    dt_med = float(np.median(np.diff(t))) if len(t) > 1 else 1.0
    # 电压方差阈值
    var_arr = _rolling_var(V, win=v_var_win)
    v_var_th = float(np.quantile(var_arr, v_q))
    # 电流局部斜率阈值（负值）
    slope = _local_slope(I, win=slope_win)
    neg = slope[slope < 0]
    if len(neg) == 0:
        i_slope_th = float(np.quantile(slope, v_q))  # 兜底
    else:
        i_slope_th = float(np.quantile(neg, slope_q))
    # 最小长度(样本)
    min_len = int(max(10, round(min_sec / max(1e-9, dt_med))))
    return v_var_th, i_slope_th, min_len

def find_cv_segments_adaptive(t, V, I, params=None):
    """先自适应校准阈值，再调用启发式切分得到 CV 段"""
    if params is None:
        params = {}
    v_var_win = params.get('v_var_win', 51)
    slope_win = params.get('slope_win', 51)
    v_q = params.get('v_q', 0.1)
    slope_q = params.get('slope_q', 0.2)
    min_sec = params.get('min_sec', 30.0)
    v_th, s_th, min_len = calibrate_cv_thresholds(t, V, I, v_var_win, v_q, slope_win, slope_q, min_sec)
    segs = find_cv_segments(t, V, I, v_var_win=v_var_win, v_var_th=v_th, i_slope_th=s_th, min_len=min_len)
    return segs, {'v_var_th': v_th, 'i_slope_th': s_th, 'min_len': min_len}

def eval_cv_segments_adaptive(t, I, V, Q, params=None):
    """自适应校准 + 批量评估软指标与 y_risk（推荐在多工况下使用）"""
    segs, th = find_cv_segments_adaptive(t, V, I, params=params)
    results = []
    for s, e in segs:
        metrics = soft_metrics_from_IVQt(t[s:e+1], I[s:e+1], V[s:e+1], Q[s:e+1], params=params)
        metrics['segment'] = (int(s), int(e))
        metrics['thresholds'] = th
        results.append(metrics)
    return results

# 用法（示例）：
# cv_results = eval_cv_segments_adaptive(t_all, I_all, V_all, Q_all,
#                                       params={'v_q':0.1, 'slope_q':0.2, 'min_sec':20})
# for r in cv_results:  # 遍历自适应切分出的 CV 段
#     print(r['segment'], r['y_risk'], r['S_amp'], r['Q_tilde'], r['thresholds'])
```

---

<a id="goal-logic"></a>
### 10. 目的与整体逻辑（通过…实现…，再通过…实现…）

- **目的**：在仅有 `I, V, Q, t` 四列数据的前提下，建立一套可解释、可在线部署、无需硬阈值的“软指标 + 风险评分”方案，准确、周到地检出恒压阶段的回嵌/析锂风险，并输出工程可用的解释信息（幅度、锂量代理、位置一致性等）。

- **整体逻辑**：
  1) 通过“电压低方差 + 电流下降斜率”的启发式，自动定位恒压(CV)阶段（`find_cv_segments`），或使用自适应分位校准增强跨工况鲁棒性（`find_cv_segments_adaptive`）。
  2) 再通过对每个 CV 段计算可微软指标（平台度、凸起度、非单调性、回嵌幅度 `S_amp`、软锂量代理 `\tilde Q_{Li}`、一致性 `C_low/C_\kappa`），融合为无硬阈值的软风险评分 `y_risk`（`soft_metrics_from_IVQt`/`eval_cv_segments`/`eval_cv_segments_adaptive`）。
  3) 进而通过批量评估与解释输出，完成快速质检与标定：
     - 在历史数据上横纵向评估，微调（或自适应估计）门限与权重；
     - 输出 `(start, end), y_risk, S_amp, \tilde Q_{Li}` 等，辅助工程确认与回溯。
  4) 最后可通过一致性蒸馏，将 `y_risk` 作为软标签，训练轻量模型或流式 Transformer，实现更强鲁棒性与在线推理（结合第3节“在线检出”架构）。

- **落地路径**：先“规则零样本”上线，逐步积累人工确认/弱标签；再“规则+模型”融合，持续用物理软指标与解释项约束与监控模型行为。

---

<a id="viz-matplotlib"></a>
### 11. 可视化示例（matplotlib）：I–t、I_base、δi+、w_low 与 y_risk

```python
import numpy as np
import matplotlib.pyplot as plt

def plot_cv_segment(t, I, V, Q, params=None, title='CV Segment Visualization'):
    """
    绘制单个 CV 段的关键曲线：
      - I–t（平滑后的电流）与基线 I_base
      - 回嵌正偏差 δi+ 与小电流权重 w_low
      - 软风险 y_risk（随时间的局部窗口输出或全段常数）
    """
    metrics = soft_metrics_from_IVQt(t, I, V, Q, params=params)
    dbg = metrics['debug']
    s = dbg['s']
    I_s = dbg['I_s']
    I_base = dbg['I_base']
    delta_pos = dbg['delta_pos']
    # 以 w_low 再计算一次，确保一致
    I0 = np.abs(I_s[0])
    sigma_i = (params or {}).get('sigma_i', 0.05)
    i_thr = 0.1 * I0
    w_low = 1.0 / (1.0 + np.exp(-(i_thr - np.abs(I_s)) / max(1e-12, sigma_i)))

    # 这里的 y_risk 来自全段融合（单值）；为了可视化，可以画成常数线
    y_risk = metrics['y_risk']

    fig, axes = plt.subplots(4, 1, figsize=(10, 10), sharex=True)

    # 1) 电流与基线
    axes[0].plot(t, I_s, label='I_s (smoothed)', color='tab:blue')
    axes[0].plot(t, I_base, label='I_base (monotone)', color='tab:orange', linestyle='--')
    axes[0].set_ylabel('Current (A)')
    axes[0].legend(loc='best')
    axes[0].grid(True, alpha=0.3)

    # 2) 正偏差 δi+
    axes[1].plot(t, delta_pos, label='delta i+ = max(I_s - I_base, 0)', color='tab:red')
    axes[1].set_ylabel('delta i+ (A)')
    axes[1].legend(loc='best')
    axes[1].grid(True, alpha=0.3)

    # 3) 小电流权重 w_low
    axes[2].plot(t, w_low, label='w_low (low-current weight)', color='tab:green')
    axes[2].set_ylabel('w_low (0~1)')
    axes[2].legend(loc='best')
    axes[2].grid(True, alpha=0.3)

    # 4) 风险评分 y_risk（用水平线表示全段融合风险）
    axes[3].plot(t, np.full_like(t, y_risk), label=f'y_risk={y_risk:.3f}', color='tab:purple')
    axes[3].set_xlabel('Time (s)')
    axes[3].set_ylabel('y_risk')
    axes[3].legend(loc='best')
    axes[3].grid(True, alpha=0.3)

    fig.suptitle(title, fontsize=14)
    plt.tight_layout()
    plt.show()

# 用法（对单个 CV 段）：
# plot_cv_segment(t_seg, I_seg, V_seg, Q_seg)

# 用法（自动切分后，展示前若干段）：
# results = eval_cv_segments_adaptive(t_all, I_all, V_all, Q_all)
# for i, r in enumerate(results[:3]):
#     s, e = r['segment']
#     plot_cv_segment(t_all[s:e+1], I_all[s:e+1], V_all[s:e+1], Q_all[s:e+1],
#                     title=f'CV Segment {i+1} {s}-{e} | y_risk={r["y_risk"]:.3f}')
```
