# Development Guidelines

## Code Quality Standards

### Code Review and Optimization
- **Global Code Audit**: Conduct comprehensive system-wide code review to identify and resolve issues systematically
- **Official Documentation First**: When uncertain about commands, APIs, or implementations, always reference official documentation before proceeding
- **Zero Debug Overhead**: Strictly prohibit adding debugging code, temporary logging, or diagnostic information that degrades performance or transfers troubleshooting burden to end users
- **Legacy Code Elimination**: Systematically remove all obsolete, unused, redundant, or deprecated code to maintain a clean and efficient codebase

### Framework Integrity
- **Architecture Preservation**: Only optimize and enhance within the existing framework structure - never modify core architecture
- **Feature Completeness**: Absolutely forbidden to reduce, simplify, or remove existing functionality or replace with alternative implementations that diminish capabilities
- **Backward Compatibility**: All modifications must preserve existing APIs, interfaces, and behavioral contracts

### Code Efficiency
- **Redundancy Elimination**: Identify and consolidate duplicate code through proper abstraction, modularization, and design patterns
- **Performance Optimization**: Prioritize algorithmic efficiency, memory usage optimization, and computational resource utilization
- **Maintainable Architecture**: Ensure code remains readable, testable, and maintainable while achieving optimal performance

### Implementation Standards
- **Evidence-Driven Development**: All code changes must be justified by identified defects, performance bottlenecks, or clear enhancement requirements
- **Incremental Refinement**: Implement improvements through small, controlled iterations that preserve system stability and functionality
- **Quality Gates**: Ensure all modifications pass comprehensive testing, code review, and validation processes before integration

### AI Assistant Specific Guidelines
- **Solution Completeness**: Provide fully functional, production-ready code without requiring user debugging or additional implementation
- **Context Awareness**: Leverage provided codebase context to ensure consistency with existing patterns, conventions, and architectural decisions
- **Explanation Clarity**: Provide concise, actionable explanations focused on implementation details rather than debugging instructions
- **Runtime Efficiency**: Prioritize algorithmic complexity improvements (O(n) optimizations) and memory optimization over quick fixes
- **Scalability Consideration**: Ensure all solutions are designed to work efficiently at production scale with realistic data volumes

### Prohibited Practices
- **Debug Code Injection**: Adding console.log, print statements, temporary logging, or any debugging code to production solutions
- **Incomplete Solutions**: Suggesting "try this and let me know if it works" approaches or providing partial implementations requiring user completion
- **Performance Degradation**: Implementing solutions that sacrifice performance for convenience or simplicity
- **Architecture Violations**: Modifying core framework structures or bypassing established architectural patterns
- **Documentation Gaps**: Leaving code without proper documentation or relying on debugging output for explanation
