# 电池析锂检测模型调试指南

## 1. 输入输出规范

### 1.1 输入数据格式规范

#### 原始数据输入格式
```json
{
  "metadata": {
    "battery_id": "BAT_001",
    "cell_type": "18650_LiCoO2",
    "nominal_capacity": 2.5,
    "temperature": 25.0,
    "cycle_number": 150
  },
  "cv_phase_data": {
    "time": [0, 10, 20, 30, ...],           // 时间序列 (秒)
    "voltage": [4.2, 4.19, 4.18, ...],      // 电压序列 (V)
    "current": [1.0, 0.95, 0.90, ...],      // 电流序列 (A)
    "capacity": [0, 0.01, 0.02, ...],       // 累积容量 (Ah)
    "temperature": [25.1, 25.2, 25.1, ...]  // 温度序列 (°C)
  }
}
```

#### 特征向量输入格式
```python
# 标准化后的特征向量
feature_vector = {
    'statistical_features': np.array([...]),      # 统计特征 (20维)
    'wavelet_features': np.array([...]),          # 小波特征 (32维)
    'electrochemical_features': np.array([...]),  # 电化学特征 (15维)
    'temporal_features': np.array([...]),         # 时序特征 (10维)
    'physics_features': np.array([...])           # 物理特征 (8维)
}
# 总特征维度: 85维
```

### 1.2 输出数据格式规范

#### 预测结果输出
```json
{
  "prediction": {
    "lithium_plating_probability": 0.87,
    "confidence_score": 0.92,
    "risk_level": "HIGH",
    "early_warning": true
  },
  "feature_importance": {
    "dqdv_peak": 0.35,
    "diff_coulombic_efficiency": 0.28,
    "voltage_plateau_duration": 0.18,
    "current_decay_rate": 0.12,
    "other": 0.07
  },
  "explanation": {
    "primary_indicators": [
      "检测到低电压区域dQ/dV异常峰值",
      "库伦效率连续3个循环下降",
      "电压平台持续时间异常延长"
    ],
    "recommendation": "建议降低充电电流至0.5C，监控后续3个循环"
  },
  "uncertainty": {
    "aleatoric_uncertainty": 0.05,  // 数据不确定性
    "epistemic_uncertainty": 0.08   // 模型不确定性
  }
}
```

## 2. 数据预处理调试

### 2.1 数据质量检查

```python
class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self):
        self.quality_thresholds = {
            'voltage_range': (2.5, 4.5),
            'current_range': (-5.0, 5.0),
            'temperature_range': (-20, 60),
            'min_data_points': 100,
            'max_noise_level': 0.1
        }
    
    def check_data_quality(self, data):
        """全面数据质量检查"""
        issues = []
        
        # 1. 数据完整性检查
        completeness_issues = self._check_completeness(data)
        issues.extend(completeness_issues)
        
        # 2. 数据范围检查
        range_issues = self._check_data_ranges(data)
        issues.extend(range_issues)
        
        # 3. 数据一致性检查
        consistency_issues = self._check_consistency(data)
        issues.extend(consistency_issues)
        
        # 4. 噪声水平检查
        noise_issues = self._check_noise_level(data)
        issues.extend(noise_issues)
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'quality_score': self._calculate_quality_score(issues)
        }
    
    def _check_completeness(self, data):
        """检查数据完整性"""
        issues = []
        
        required_fields = ['time', 'voltage', 'current', 'capacity']
        for field in required_fields:
            if field not in data or len(data[field]) == 0:
                issues.append(f"缺失必要字段: {field}")
            elif np.any(np.isnan(data[field])):
                nan_count = np.sum(np.isnan(data[field]))
                issues.append(f"字段 {field} 包含 {nan_count} 个NaN值")
        
        return issues
    
    def _check_data_ranges(self, data):
        """检查数据范围"""
        issues = []
        
        # 电压范围检查
        voltage = data.get('voltage', [])
        if len(voltage) > 0:
            v_min, v_max = np.min(voltage), np.max(voltage)
            expected_min, expected_max = self.quality_thresholds['voltage_range']
            if v_min < expected_min or v_max > expected_max:
                issues.append(f"电压超出合理范围: [{v_min:.3f}, {v_max:.3f}]V")
        
        # 电流范围检查
        current = data.get('current', [])
        if len(current) > 0:
            i_min, i_max = np.min(current), np.max(current)
            expected_min, expected_max = self.quality_thresholds['current_range']
            if i_min < expected_min or i_max > expected_max:
                issues.append(f"电流超出合理范围: [{i_min:.3f}, {i_max:.3f}]A")
        
        return issues
    
    def _check_consistency(self, data):
        """检查数据一致性"""
        issues = []
        
        # 时间序列单调性检查
        time = data.get('time', [])
        if len(time) > 1:
            time_diff = np.diff(time)
            if np.any(time_diff <= 0):
                issues.append("时间序列非单调递增")
        
        # 容量单调性检查（充电阶段）
        capacity = data.get('capacity', [])
        current = data.get('current', [])
        if len(capacity) > 1 and len(current) > 0:
            charging_mask = current > 0
            if np.any(charging_mask):
                charging_capacity = capacity[charging_mask]
                if len(charging_capacity) > 1:
                    cap_diff = np.diff(charging_capacity)
                    if np.any(cap_diff < 0):
                        issues.append("充电阶段容量非单调递增")
        
        return issues
```

### 2.2 恒压阶段提取调试

```python
class CVPhaseExtractor:
    """恒压阶段提取器"""
    
    def __init__(self):
        self.cv_criteria = {
            'voltage_std_threshold': 0.01,      # 电压标准差阈值
            'current_decay_threshold': -0.001,  # 电流衰减阈值
            'min_duration': 60,                 # 最小持续时间(秒)
            'min_data_points': 50               # 最小数据点数
        }
        
        self.debug_mode = True
    
    def extract_cv_phase(self, cycle_data):
        """提取恒压阶段并提供调试信息"""
        debug_info = {
            'total_data_points': len(cycle_data['time']),
            'extraction_steps': [],
            'cv_segments': [],
            'rejection_reasons': []
        }
        
        voltage = np.array(cycle_data['voltage'])
        current = np.array(cycle_data['current'])
        time = np.array(cycle_data['time'])
        
        # 步骤1: 识别电压稳定区域
        voltage_stable_mask = self._identify_voltage_stable_regions(voltage, debug_info)
        
        # 步骤2: 识别电流衰减区域
        current_decay_mask = self._identify_current_decay_regions(current, debug_info)
        
        # 步骤3: 合并条件
        cv_mask = voltage_stable_mask & current_decay_mask
        debug_info['extraction_steps'].append(f"合并条件后候选点数: {np.sum(cv_mask)}")
        
        # 步骤4: 提取连续段
        cv_segments = self._extract_continuous_segments(cv_mask, time, debug_info)
        
        # 步骤5: 过滤短段
        valid_segments = self._filter_short_segments(cv_segments, debug_info)
        
        if self.debug_mode:
            self._plot_extraction_debug(cycle_data, cv_mask, valid_segments)
        
        return valid_segments, debug_info
    
    def _identify_voltage_stable_regions(self, voltage, debug_info):
        """识别电压稳定区域"""
        window_size = min(20, len(voltage) // 10)
        voltage_std = pd.Series(voltage).rolling(window=window_size).std()
        
        stable_mask = voltage_std < self.cv_criteria['voltage_std_threshold']
        stable_count = np.sum(stable_mask)
        
        debug_info['extraction_steps'].append(
            f"电压稳定区域识别: {stable_count}/{len(voltage)} 点"
        )
        
        return stable_mask.fillna(False).values
    
    def _plot_extraction_debug(self, cycle_data, cv_mask, valid_segments):
        """绘制提取过程调试图"""
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        time = cycle_data['time']
        voltage = cycle_data['voltage']
        current = cycle_data['current']
        
        # 电压图
        axes[0].plot(time, voltage, 'b-', alpha=0.7, label='电压')
        axes[0].scatter(np.array(time)[cv_mask], np.array(voltage)[cv_mask], 
                       c='red', s=10, label='CV候选点')
        axes[0].set_ylabel('电压 (V)')
        axes[0].legend()
        axes[0].grid(True)
        
        # 电流图
        axes[1].plot(time, current, 'g-', alpha=0.7, label='电流')
        axes[1].scatter(np.array(time)[cv_mask], np.array(current)[cv_mask], 
                       c='red', s=10, label='CV候选点')
        axes[1].set_ylabel('电流 (A)')
        axes[1].legend()
        axes[1].grid(True)
        
        # CV段标记
        axes[2].plot(time, voltage, 'b-', alpha=0.3)
        for i, segment in enumerate(valid_segments):
            start_idx, end_idx = segment['start_idx'], segment['end_idx']
            axes[2].plot(time[start_idx:end_idx], voltage[start_idx:end_idx], 
                        linewidth=3, label=f'CV段{i+1}')
        axes[2].set_xlabel('时间 (s)')
        axes[2].set_ylabel('电压 (V)')
        axes[2].legend()
        axes[2].grid(True)
        
        plt.tight_layout()
        plt.savefig('cv_extraction_debug.png', dpi=300, bbox_inches='tight')
        plt.show()
```

## 3. 特征工程调试

### 3.1 特征提取验证

```python
class FeatureExtractionValidator:
    """特征提取验证器"""
    
    def __init__(self):
        self.feature_ranges = {
            'coulombic_efficiency': (0.8, 1.1),
            'dqdv_peak': (0, 1000),
            'voltage_plateau_duration': (0, 3600),
            'current_decay_rate': (-1, 0),
            'wavelet_energy': (0, np.inf)
        }
    
    def validate_features(self, features, feature_names):
        """验证提取的特征"""
        validation_results = {
            'valid_features': [],
            'invalid_features': [],
            'warnings': [],
            'statistics': {}
        }
        
        for i, (feature_name, feature_value) in enumerate(zip(feature_names, features)):
            # 检查数值有效性
            if np.isnan(feature_value) or np.isinf(feature_value):
                validation_results['invalid_features'].append({
                    'name': feature_name,
                    'value': feature_value,
                    'issue': 'NaN或Inf值'
                })
                continue
            
            # 检查合理范围
            if feature_name in self.feature_ranges:
                min_val, max_val = self.feature_ranges[feature_name]
                if not (min_val <= feature_value <= max_val):
                    validation_results['warnings'].append({
                        'name': feature_name,
                        'value': feature_value,
                        'expected_range': (min_val, max_val),
                        'issue': '超出预期范围'
                    })
            
            validation_results['valid_features'].append({
                'name': feature_name,
                'value': feature_value
            })
        
        # 计算特征统计信息
        valid_values = [f['value'] for f in validation_results['valid_features']]
        if valid_values:
            validation_results['statistics'] = {
                'mean': np.mean(valid_values),
                'std': np.std(valid_values),
                'min': np.min(valid_values),
                'max': np.max(valid_values),
                'zero_count': np.sum(np.array(valid_values) == 0),
                'negative_count': np.sum(np.array(valid_values) < 0)
            }
        
        return validation_results
    
    def plot_feature_distribution(self, features_batch, feature_names):
        """绘制特征分布图"""
        import matplotlib.pyplot as plt
        
        n_features = len(feature_names)
        n_cols = 4
        n_rows = (n_features + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(16, 4*n_rows))
        axes = axes.flatten() if n_rows > 1 else [axes]
        
        for i, (feature_name, feature_values) in enumerate(zip(feature_names, features_batch.T)):
            ax = axes[i]
            
            # 过滤有效值
            valid_values = feature_values[~(np.isnan(feature_values) | np.isinf(feature_values))]
            
            if len(valid_values) > 0:
                ax.hist(valid_values, bins=30, alpha=0.7, edgecolor='black')
                ax.set_title(f'{feature_name}\n(μ={np.mean(valid_values):.3f}, σ={np.std(valid_values):.3f})')
                ax.set_xlabel('特征值')
                ax.set_ylabel('频次')
                ax.grid(True, alpha=0.3)
            else:
                ax.text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{feature_name} - 无效')
        
        # 隐藏多余的子图
        for i in range(n_features, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('feature_distribution_debug.png', dpi=300, bbox_inches='tight')
        plt.show()
```

### 3.2 特征相关性分析

```python
class FeatureCorrelationAnalyzer:
    """特征相关性分析器"""
    
    def analyze_correlations(self, features, feature_names, labels=None):
        """分析特征相关性"""
        import pandas as pd
        import seaborn as sns
        import matplotlib.pyplot as plt
        
        # 创建DataFrame
        df = pd.DataFrame(features, columns=feature_names)
        if labels is not None:
            df['lithium_plating'] = labels
        
        # 计算相关性矩阵
        correlation_matrix = df.corr()
        
        # 识别高相关性特征对
        high_corr_pairs = self._find_high_correlation_pairs(correlation_matrix)
        
        # 分析特征与标签的相关性
        label_correlations = None
        if labels is not None:
            label_correlations = correlation_matrix['lithium_plating'].drop('lithium_plating')
            label_correlations = label_correlations.sort_values(key=abs, ascending=False)
        
        # 绘制相关性热图
        self._plot_correlation_heatmap(correlation_matrix)
        
        return {
            'correlation_matrix': correlation_matrix,
            'high_correlation_pairs': high_corr_pairs,
            'label_correlations': label_correlations
        }
    
    def _find_high_correlation_pairs(self, corr_matrix, threshold=0.8):
        """找出高相关性特征对"""
        high_corr_pairs = []
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_value = corr_matrix.iloc[i, j]
                if abs(corr_value) > threshold:
                    high_corr_pairs.append({
                        'feature1': corr_matrix.columns[i],
                        'feature2': corr_matrix.columns[j],
                        'correlation': corr_value
                    })
        
        return sorted(high_corr_pairs, key=lambda x: abs(x['correlation']), reverse=True)
    
    def _plot_correlation_heatmap(self, corr_matrix):
        """绘制相关性热图"""
        plt.figure(figsize=(12, 10))
        
        # 创建掩码以隐藏上三角
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        
        # 绘制热图
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": 0.8})
        
        plt.title('特征相关性热图')
        plt.tight_layout()
        plt.savefig('feature_correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
```

## 4. 模型训练调试

### 4.1 训练过程监控

```python
class TrainingMonitor:
    """训练过程监控器"""
    
    def __init__(self):
        self.metrics_history = {
            'train_loss': [],
            'val_loss': [],
            'train_accuracy': [],
            'val_accuracy': [],
            'physics_loss': [],
            'data_loss': []
        }
        
        self.early_stopping_patience = 10
        self.best_val_loss = np.inf
        self.patience_counter = 0
    
    def on_epoch_end(self, epoch, logs):
        """每个epoch结束时的回调"""
        # 记录指标
        for metric, value in logs.items():
            if metric in self.metrics_history:
                self.metrics_history[metric].append(value)
        
        # 检查早停条件
        current_val_loss = logs.get('val_loss', np.inf)
        if current_val_loss < self.best_val_loss:
            self.best_val_loss = current_val_loss
            self.patience_counter = 0
        else:
            self.patience_counter += 1
        
        # 每10个epoch绘制一次训练曲线
        if (epoch + 1) % 10 == 0:
            self.plot_training_curves()
        
        # 检查异常情况
        self._check_training_anomalies(logs)
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 损失曲线
        axes[0, 0].plot(self.metrics_history['train_loss'], label='训练损失')
        axes[0, 0].plot(self.metrics_history['val_loss'], label='验证损失')
        axes[0, 0].set_title('损失曲线')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 准确率曲线
        axes[0, 1].plot(self.metrics_history['train_accuracy'], label='训练准确率')
        axes[0, 1].plot(self.metrics_history['val_accuracy'], label='验证准确率')
        axes[0, 1].set_title('准确率曲线')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('准确率')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 物理损失 vs 数据损失
        if self.metrics_history['physics_loss'] and self.metrics_history['data_loss']:
            axes[1, 0].plot(self.metrics_history['physics_loss'], label='物理损失')
            axes[1, 0].plot(self.metrics_history['data_loss'], label='数据损失')
            axes[1, 0].set_title('损失组件')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('损失')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
        
        # 学习率曲线
        if 'learning_rate' in self.metrics_history:
            axes[1, 1].plot(self.metrics_history['learning_rate'])
            axes[1, 1].set_title('学习率')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('学习率')
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig(f'training_curves_epoch_{len(self.metrics_history["train_loss"])}.png', 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def _check_training_anomalies(self, logs):
        """检查训练异常"""
        warnings = []
        
        # 检查损失爆炸
        current_loss = logs.get('train_loss', 0)
        if current_loss > 10:
            warnings.append(f"训练损失过高: {current_loss:.4f}")
        
        # 检查梯度消失/爆炸
        if 'gradient_norm' in logs:
            grad_norm = logs['gradient_norm']
            if grad_norm < 1e-6:
                warnings.append(f"梯度过小，可能出现梯度消失: {grad_norm:.2e}")
            elif grad_norm > 100:
                warnings.append(f"梯度过大，可能出现梯度爆炸: {grad_norm:.2e}")
        
        # 检查过拟合
        if len(self.metrics_history['train_loss']) > 5:
            recent_train_loss = np.mean(self.metrics_history['train_loss'][-5:])
            recent_val_loss = np.mean(self.metrics_history['val_loss'][-5:])
            if recent_val_loss > recent_train_loss * 1.5:
                warnings.append("可能出现过拟合，验证损失远高于训练损失")
        
        # 输出警告
        for warning in warnings:
            print(f"⚠️  训练警告: {warning}")
```

### 4.2 梯度和权重分析

```python
class GradientWeightAnalyzer:
    """梯度和权重分析器"""
    
    def __init__(self, model):
        self.model = model
        self.gradient_history = []
        self.weight_history = []
    
    def analyze_gradients(self, inputs, targets):
        """分析梯度"""
        with tf.GradientTape() as tape:
            predictions = self.model(inputs, training=True)
            loss = self.model.compiled_loss(targets, predictions)
        
        gradients = tape.gradient(loss, self.model.trainable_variables)
        
        gradient_stats = {}
        for i, (var, grad) in enumerate(zip(self.model.trainable_variables, gradients)):
            if grad is not None:
                grad_norm = tf.norm(grad).numpy()
                grad_mean = tf.reduce_mean(tf.abs(grad)).numpy()
                grad_max = tf.reduce_max(tf.abs(grad)).numpy()
                
                gradient_stats[f'layer_{i}_{var.name}'] = {
                    'norm': grad_norm,
                    'mean_abs': grad_mean,
                    'max_abs': grad_max,
                    'shape': var.shape.as_list()
                }
        
        self.gradient_history.append(gradient_stats)
        return gradient_stats
    
    def analyze_weights(self):
        """分析权重分布"""
        weight_stats = {}
        
        for i, var in enumerate(self.model.trainable_variables):
            weights = var.numpy()
            
            weight_stats[f'layer_{i}_{var.name}'] = {
                'mean': np.mean(weights),
                'std': np.std(weights),
                'min': np.min(weights),
                'max': np.max(weights),
                'zero_fraction': np.mean(weights == 0),
                'shape': weights.shape
            }
        
        self.weight_history.append(weight_stats)
        return weight_stats
    
    def plot_gradient_flow(self):
        """绘制梯度流图"""
        if not self.gradient_history:
            print("没有梯度历史数据")
            return
        
        # 提取每层的梯度范数
        layer_names = list(self.gradient_history[-1].keys())
        gradient_norms = []
        
        for layer_name in layer_names:
            norms = [epoch_grads[layer_name]['norm'] for epoch_grads in self.gradient_history]
            gradient_norms.append(norms)
        
        # 绘制梯度流
        plt.figure(figsize=(12, 6))
        
        for i, (layer_name, norms) in enumerate(zip(layer_names, gradient_norms)):
            plt.plot(norms, label=f'{layer_name[:20]}...', alpha=0.7)
        
        plt.title('梯度范数随训练变化')
        plt.xlabel('Epoch')
        plt.ylabel('梯度范数')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig('gradient_flow.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def detect_gradient_issues(self):
        """检测梯度问题"""
        if not self.gradient_history:
            return []
        
        issues = []
        latest_grads = self.gradient_history[-1]
        
        for layer_name, grad_stats in latest_grads.items():
            # 检测梯度消失
            if grad_stats['norm'] < 1e-6:
                issues.append(f"梯度消失: {layer_name}, 范数={grad_stats['norm']:.2e}")
            
            # 检测梯度爆炸
            if grad_stats['norm'] > 100:
                issues.append(f"梯度爆炸: {layer_name}, 范数={grad_stats['norm']:.2e}")
            
            # 检测死神经元
            if grad_stats['mean_abs'] < 1e-8:
                issues.append(f"可能的死神经元: {layer_name}, 平均梯度={grad_stats['mean_abs']:.2e}")
        
        return issues
```

## 5. 模型评估调试

### 5.1 预测结果分析

```python
class PredictionAnalyzer:
    """预测结果分析器"""
    
    def __init__(self):
        self.threshold = 0.5
    
    def analyze_predictions(self, y_true, y_pred, y_prob=None):
        """全面分析预测结果"""
        from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
        
        analysis_results = {}
        
        # 基本分类指标
        analysis_results['classification_report'] = classification_report(y_true, y_pred, output_dict=True)
        analysis_results['confusion_matrix'] = confusion_matrix(y_true, y_pred)
        
        # ROC分析
        if y_prob is not None:
            analysis_results['roc_auc'] = roc_auc_score(y_true, y_prob)
            fpr, tpr, thresholds = roc_curve(y_true, y_prob)
            analysis_results['roc_curve'] = {'fpr': fpr, 'tpr': tpr, 'thresholds': thresholds}
        
        # 错误分析
        analysis_results['error_analysis'] = self._analyze_errors(y_true, y_pred, y_prob)
        
        # 置信度分析
        if y_prob is not None:
            analysis_results['confidence_analysis'] = self._analyze_confidence(y_true, y_prob)
        
        return analysis_results
    
    def _analyze_errors(self, y_true, y_pred, y_prob=None):
        """分析预测错误"""
        # 找出错误预测的样本
        error_mask = y_true != y_pred
        error_indices = np.where(error_mask)[0]
        
        # 分类错误类型
        false_positives = np.where((y_true == 0) & (y_pred == 1))[0]
        false_negatives = np.where((y_true == 1) & (y_pred == 0))[0]
        
        error_analysis = {
            'total_errors': len(error_indices),
            'false_positives': len(false_positives),
            'false_negatives': len(false_negatives),
            'error_rate': len(error_indices) / len(y_true),
            'fp_indices': false_positives.tolist(),
            'fn_indices': false_negatives.tolist()
        }
        
        # 如果有概率信息，分析错误样本的置信度
        if y_prob is not None:
            fp_confidences = y_prob[false_positives] if len(false_positives) > 0 else []
            fn_confidences = 1 - y_prob[false_negatives] if len(false_negatives) > 0 else []
            
            error_analysis['fp_avg_confidence'] = np.mean(fp_confidences) if len(fp_confidences) > 0 else 0
            error_analysis['fn_avg_confidence'] = np.mean(fn_confidences) if len(fn_confidences) > 0 else 0
        
        return error_analysis
    
    def _analyze_confidence(self, y_true, y_prob):
        """分析预测置信度"""
        # 计算预测置信度（距离0.5的距离）
        confidence = np.abs(y_prob - 0.5) * 2
        
        # 按真实标签分组分析
        positive_confidence = confidence[y_true == 1]
        negative_confidence = confidence[y_true == 0]
        
        confidence_analysis = {
            'overall_avg_confidence': np.mean(confidence),
            'positive_avg_confidence': np.mean(positive_confidence),
            'negative_avg_confidence': np.mean(negative_confidence),
            'low_confidence_samples': np.sum(confidence < 0.6),
            'high_confidence_samples': np.sum(confidence > 0.8)
        }
        
        return confidence_analysis
    
    def plot_prediction_analysis(self, y_true, y_pred, y_prob=None):
        """绘制预测分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0, 0])
        axes[0, 0].set_title('混淆矩阵')
        axes[0, 0].set_xlabel('预测标签')
        axes[0, 0].set_ylabel('真实标签')
        
        # ROC曲线
        if y_prob is not None:
            fpr, tpr, _ = roc_curve(y_true, y_prob)
            auc_score = roc_auc_score(y_true, y_prob)
            axes[0, 1].plot(fpr, tpr, label=f'ROC (AUC = {auc_score:.3f})')
            axes[0, 1].plot([0, 1], [0, 1], 'k--', label='随机分类器')
            axes[0, 1].set_title('ROC曲线')
            axes[0, 1].set_xlabel('假正率')
            axes[0, 1].set_ylabel('真正率')
            axes[0, 1].legend()
            axes[0, 1].grid(True)
        
        # 预测概率分布
        if y_prob is not None:
            axes[1, 0].hist(y_prob[y_true == 0], bins=30, alpha=0.7, label='负样本', color='red')
            axes[1, 0].hist(y_prob[y_true == 1], bins=30, alpha=0.7, label='正样本', color='blue')
            axes[1, 0].axvline(x=0.5, color='black', linestyle='--', label='阈值')
            axes[1, 0].set_title('预测概率分布')
            axes[1, 0].set_xlabel('预测概率')
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].legend()
        
        # 置信度分析
        if y_prob is not None:
            confidence = np.abs(y_prob - 0.5) * 2
            correct_mask = y_true == y_pred
            
            axes[1, 1].scatter(confidence[correct_mask], y_prob[correct_mask], 
                             alpha=0.6, label='正确预测', color='green')
            axes[1, 1].scatter(confidence[~correct_mask], y_prob[~correct_mask], 
                             alpha=0.6, label='错误预测', color='red')
            axes[1, 1].set_title('置信度 vs 预测概率')
            axes[1, 1].set_xlabel('置信度')
            axes[1, 1].set_ylabel('预测概率')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig('prediction_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
```

### 5.2 模型可解释性分析

```python
class ModelExplainabilityAnalyzer:
    """模型可解释性分析器"""
    
    def __init__(self, model, feature_names):
        self.model = model
        self.feature_names = feature_names
    
    def analyze_feature_importance(self, X, y, method='shap'):
        """分析特征重要性"""
        if method == 'shap':
            return self._shap_analysis(X, y)
        elif method == 'permutation':
            return self._permutation_importance(X, y)
        elif method == 'gradient':
            return self._gradient_importance(X, y)
        else:
            raise ValueError(f"不支持的方法: {method}")
    
    def _shap_analysis(self, X, y):
        """SHAP分析"""
        try:
            import shap
            
            # 创建解释器
            explainer = shap.DeepExplainer(self.model, X[:100])  # 使用前100个样本作为背景
            shap_values = explainer.shap_values(X[:500])  # 解释前500个样本
            
            # 计算特征重要性
            if isinstance(shap_values, list):
                shap_values = shap_values[0]  # 二分类情况
            
            feature_importance = np.mean(np.abs(shap_values), axis=0)
            
            # 创建重要性排序
            importance_ranking = sorted(
                zip(self.feature_names, feature_importance),
                key=lambda x: x[1], reverse=True
            )
            
            # 绘制SHAP图
            self._plot_shap_summary(shap_values, X[:500])
            
            return {
                'method': 'SHAP',
                'feature_importance': dict(importance_ranking),
                'shap_values': shap_values,
                'ranking': importance_ranking
            }
            
        except ImportError:
            print("SHAP库未安装，使用梯度方法替代")
            return self._gradient_importance(X, y)
    
    def _gradient_importance(self, X, y):
        """基于梯度的特征重要性"""
        import tensorflow as tf
        
        gradients_list = []
        
        for i in range(min(len(X), 1000)):  # 限制样本数量以提高效率
            with tf.GradientTape() as tape:
                x_tensor = tf.convert_to_tensor(X[i:i+1], dtype=tf.float32)
                tape.watch(x_tensor)
                prediction = self.model(x_tensor)
                
            gradient = tape.gradient(prediction, x_tensor)
            gradients_list.append(gradient.numpy().flatten())
        
        # 计算平均梯度幅度
        avg_gradients = np.mean(np.abs(gradients_list), axis=0)
        
        # 创建重要性排序
        importance_ranking = sorted(
            zip(self.feature_names, avg_gradients),
            key=lambda x: x[1], reverse=True
        )
        
        return {
            'method': 'Gradient',
            'feature_importance': dict(importance_ranking),
            'ranking': importance_ranking
        }
    
    def _plot_shap_summary(self, shap_values, X_sample):
        """绘制SHAP摘要图"""
        try:
            import shap
            
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_values, X_sample, feature_names=self.feature_names, show=False)
            plt.title('SHAP特征重要性摘要')
            plt.tight_layout()
            plt.savefig('shap_summary.png', dpi=300, bbox_inches='tight')
            plt.show()
            
        except Exception as e:
            print(f"绘制SHAP图时出错: {e}")
    
    def analyze_prediction_explanation(self, sample_data, sample_idx=0):
        """分析单个样本的预测解释"""
        sample = sample_data[sample_idx:sample_idx+1]
        prediction = self.model.predict(sample)[0]
        
        # 获取特征重要性
        importance_result = self.analyze_feature_importance(sample_data[:100], None)
        
        # 分析当前样本的特征值
        sample_features = sample.flatten()
        
        explanation = {
            'prediction': prediction,
            'confidence': abs(prediction - 0.5) * 2,
            'sample_features': dict(zip(self.feature_names, sample_features)),
            'feature_contributions': {}
        }
        
        # 计算特征贡献
        for feature_name, importance in importance_result['feature_importance'].items():
            feature_idx = self.feature_names.index(feature_name)
            feature_value = sample_features[feature_idx]
            contribution = importance * feature_value
            explanation['feature_contributions'][feature_name] = contribution
        
        # 排序贡献
        sorted_contributions = sorted(
            explanation['feature_contributions'].items(),
            key=lambda x: abs(x[1]), reverse=True
        )
        
        explanation['top_contributors'] = sorted_contributions[:10]
        
        return explanation
```

## 6. 常见问题诊断

### 6.1 数据问题诊断

```python
class DataIssueDiagnoser:
    """数据问题诊断器"""
    
    def diagnose_data_issues(self, data, labels=None):
        """诊断数据问题"""
        issues = []
        
        # 1. 数据分布问题
        distribution_issues = self._check_data_distribution(data)
        issues.extend(distribution_issues)
        
        # 2. 标签分布问题
        if labels is not None:
            label_issues = self._check_label_distribution(labels)
            issues.extend(label_issues)
        
        # 3. 数据泄露检查
        leakage_issues = self._check_data_leakage(data)
        issues.extend(leakage_issues)
        
        # 4. 异常值检查
        outlier_issues = self._check_outliers(data)
        issues.extend(outlier_issues)
        
        return {
            'total_issues': len(issues),
            'issues': issues,
            'severity_distribution': self._categorize_issues(issues)
        }
    
    def _check_data_distribution(self, data):
        """检查数据分布问题"""
        issues = []
        
        # 检查特征方差
        feature_vars = np.var(data, axis=0)
        low_variance_features = np.where(feature_vars < 1e-6)[0]
        
        if len(low_variance_features) > 0:
            issues.append({
                'type': '低方差特征',
                'severity': 'medium',
                'description': f'发现{len(low_variance_features)}个低方差特征',
                'affected_features': low_variance_features.tolist()
            })
        
        # 检查特征偏度
        from scipy.stats import skew
        feature_skewness = skew(data, axis=0)
        highly_skewed = np.where(np.abs(feature_skewness) > 3)[0]
        
        if len(highly_skewed) > 0:
            issues.append({
                'type': '高偏度特征',
                'severity': 'low',
                'description': f'发现{len(highly_skewed)}个高偏度特征',
                'affected_features': highly_skewed.tolist()
            })
        
        return issues
    
    def _check_label_distribution(self, labels):
        """检查标签分布问题"""
        issues = []
        
        # 检查类别不平衡
        unique_labels, counts = np.unique(labels, return_counts=True)
        imbalance_ratio = max(counts) / min(counts)
        
        if imbalance_ratio > 10:
            issues.append({
                'type': '严重类别不平衡',
                'severity': 'high',
                'description': f'类别不平衡比例: {imbalance_ratio:.2f}',
                'label_distribution': dict(zip(unique_labels, counts))
            })
        elif imbalance_ratio > 3:
            issues.append({
                'type': '中等类别不平衡',
                'severity': 'medium',
                'description': f'类别不平衡比例: {imbalance_ratio:.2f}',
                'label_distribution': dict(zip(unique_labels, counts))
            })
        
        return issues
    
    def _check_outliers(self, data):
        """检查异常值"""
        issues = []
        
        # 使用IQR方法检测异常值
        Q1 = np.percentile(data, 25, axis=0)
        Q3 = np.percentile(data, 75, axis=0)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outlier_mask = (data < lower_bound) | (data > upper_bound)
        outlier_counts = np.sum(outlier_mask, axis=0)
        
        high_outlier_features = np.where(outlier_counts > len(data) * 0.05)[0]
        
        if len(high_outlier_features) > 0:
            issues.append({
                'type': '高异常值比例',
                'severity': 'medium',
                'description': f'{len(high_outlier_features)}个特征的异常值比例超过5%',
                'affected_features': high_outlier_features.tolist(),
                'outlier_counts': outlier_counts[high_outlier_features].tolist()
            })
        
        return issues
```

### 6.2 模型性能问题诊断

```python
class ModelPerformanceDiagnoser:
    """模型性能问题诊断器"""
    
    def diagnose_performance_issues(self, model, X_train, y_train, X_val, y_val):
        """诊断模型性能问题"""
        issues = []
        
        # 获取预测结果
        train_pred = model.predict(X_train)
        val_pred = model.predict(X_val)
        
        # 1. 过拟合/欠拟合检查
        fitting_issues = self._check_fitting_issues(y_train, train_pred, y_val, val_pred)
        issues.extend(fitting_issues)
        
        # 2. 收敛问题检查
        convergence_issues = self._check_convergence_issues(model)
        issues.extend(convergence_issues)
        
        # 3. 预测分布问题
        prediction_issues = self._check_prediction_distribution(train_pred, val_pred)
        issues.extend(prediction_issues)
        
        return {
            'total_issues': len(issues),
            'issues': issues,
            'recommendations': self._generate_recommendations(issues)
        }
    
    def _check_fitting_issues(self, y_train, train_pred, y_val, val_pred):
        """检查拟合问题"""
        issues = []
        
        # 计算训练和验证准确率
        train_acc = np.mean((train_pred > 0.5) == y_train)
        val_acc = np.mean((val_pred > 0.5) == y_val)
        
        # 过拟合检查
        if train_acc - val_acc > 0.15:
            issues.append({
                'type': '过拟合',
                'severity': 'high',
                'description': f'训练准确率({train_acc:.3f})远高于验证准确率({val_acc:.3f})',
                'train_accuracy': train_acc,
                'val_accuracy': val_acc
            })
        
        # 欠拟合检查
        if train_acc < 0.6:
            issues.append({
                'type': '欠拟合',
                'severity': 'high',
                'description': f'训练准确率过低({train_acc:.3f})',
                'train_accuracy': train_acc
            })
        
        return issues
    
    def _generate_recommendations(self, issues):
        """生成改进建议"""
        recommendations = []
        
        for issue in issues:
            if issue['type'] == '过拟合':
                recommendations.extend([
                    '增加正则化强度',
                    '使用Dropout',
                    '减少模型复杂度',
                    '增加训练数据',
                    '使用早停策略'
                ])
            elif issue['type'] == '欠拟合':
                recommendations.extend([
                    '增加模型复杂度',
                    '减少正则化强度',
                    '增加训练轮数',
                    '检查特征工程',
                    '调整学习率'
                ])
            elif issue['type'] == '严重类别不平衡':
                recommendations.extend([
                    '使用类别权重',
                    '过采样少数类',
                    '欠采样多数类',
                    '使用SMOTE',
                    '调整分类阈值'
                ])
        
        return list(set(recommendations))  # 去重
```

## 7. 自动化调试工具

### 7.1 综合调试报告生成器

```python
class ComprehensiveDebugger:
    """综合调试器"""
    
    def __init__(self, model, feature_names):
        self.model = model
        self.feature_names = feature_names
        self.data_checker = DataQualityChecker()
        self.feature_validator = FeatureExtractionValidator()
        self.performance_diagnoser = ModelPerformanceDiagnoser()
        self.explainability_analyzer = ModelExplainabilityAnalyzer(model, feature_names)
    
    def generate_debug_report(self, X_train, y_train, X_val, y_val, raw_data=None):
        """生成综合调试报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'model_info': self._get_model_info(),
            'data_quality': {},
            'feature_analysis': {},
            'model_performance': {},
            'explainability': {},
            'recommendations': []
        }
        
        print("🔍 开始生成调试报告...")
        
        # 1. 数据质量检查
        print("📊 检查数据质量...")
        if raw_data is not None:
            report['data_quality'] = self.data_checker.check_data_quality(raw_data)
        
        # 2. 特征分析
        print("🔧 分析特征...")
        feature_validation = self.feature_validator.validate_features(
            X_train[0], self.feature_names
        )
        report['feature_analysis'] = feature_validation
        
        # 3. 模型性能诊断
        print("📈 诊断模型性能...")
        performance_diagnosis = self.performance_diagnoser.diagnose_performance_issues(
            self.model, X_train, y_train, X_val, y_val
        )
        report['model_performance'] = performance_diagnosis
        
        # 4. 可解释性分析
        print("🧠 分析模型可解释性...")
        try:
            explainability = self.explainability_analyzer.analyze_feature_importance(
                X_train[:500], y_train[:500]
            )
            report['explainability'] = explainability
        except Exception as e:
            report['explainability'] = {'error': str(e)}
        
        # 5. 生成综合建议
        print("💡 生成改进建议...")
        report['recommendations'] = self._generate_comprehensive_recommendations(report)
        
        # 6. 保存报告
        self._save_report(report)
        
        print("✅ 调试报告生成完成!")
        return report
    
    def _get_model_info(self):
        """获取模型信息"""
        try:
            return {
                'total_params': self.model.count_params(),
                'trainable_params': sum([tf.size(var).numpy() for var in self.model.trainable_variables]),
                'layers': len(self.model.layers),
                'input_shape': self.model.input_shape,
                'output_shape': self.model.output_shape
            }
        except:
            return {'error': '无法获取模型信息'}
    
    def _generate_comprehensive_recommendations(self, report):
        """生成综合改进建议"""
        recommendations = []
        
        # 基于数据质量问题的建议
        if 'issues' in report['data_quality']:
            for issue in report['data_quality']['issues']:
                if 'NaN' in issue:
                    recommendations.append("处理缺失值：使用插值或删除含NaN的样本")
                elif '超出合理范围' in issue:
                    recommendations.append("检查数据预处理：标准化或归一化异常值")
        
        # 基于特征分析的建议
        if 'invalid_features' in report['feature_analysis']:
            if report['feature_analysis']['invalid_features']:
                recommendations.append("修复特征提取：处理无效特征值")
        
        # 基于性能问题的建议
        if 'recommendations' in report['model_performance']:
            recommendations.extend(report['model_performance']['recommendations'])
        
        # 基于可解释性的建议
        if 'ranking' in report.get('explainability', {}):
            top_features = report['explainability']['ranking'][:5]
            recommendations.append(f"重点关注前5个重要特征: {[f[0] for f in top_features]}")
        
        return list(set(recommendations))
    
    def _save_report(self, report):
        """保存调试报告"""
        import json
        
        # 保存JSON格式报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"debug_report_{timestamp}.json"
        
        # 处理不可序列化的对象
        serializable_report = self._make_serializable(report)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 调试报告已保存至: {filename}")
    
    def _make_serializable(self, obj):
        """使对象可序列化"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        else:
            return obj
```

### 7.2 实时监控仪表板

```python
class RealTimeMonitoringDashboard:
    """实时监控仪表板"""
    
    def __init__(self, model, feature_names):
        self.model = model
        self.feature_names = feature_names
        self.metrics_buffer = {
            'predictions': [],
            'confidences': [],
            'feature_values': [],
            'timestamps': []
        }
        self.alert_thresholds = {
            'low_confidence': 0.6,
            'prediction_drift': 0.1,
            'feature_drift': 2.0
        }
    
    def update_metrics(self, new_data, prediction, confidence):
        """更新监控指标"""
        current_time = datetime.now()
        
        # 添加新数据
        self.metrics_buffer['predictions'].append(prediction)
        self.metrics_buffer['confidences'].append(confidence)
        self.metrics_buffer['feature_values'].append(new_data)
        self.metrics_buffer['timestamps'].append(current_time)
        
        # 保持缓冲区大小
        max_buffer_size = 1000
        if len(self.metrics_buffer['predictions']) > max_buffer_size:
            for key in self.metrics_buffer:
                self.metrics_buffer[key] = self.metrics_buffer[key][-max_buffer_size:]
        
        # 检查警报条件
        alerts = self._check_alerts()
        
        return alerts
    
    def _check_alerts(self):
        """检查警报条件"""
        alerts = []
        
        if len(self.metrics_buffer['predictions']) < 10:
            return alerts
        
        # 检查低置信度预测
        recent_confidences = self.metrics_buffer['confidences'][-10:]
        low_confidence_count = sum(1 for c in recent_confidences if c < self.alert_thresholds['low_confidence'])
        
        if low_confidence_count > 5:
            alerts.append({
                'type': 'low_confidence',
                'severity': 'medium',
                'message': f'最近10个预测中有{low_confidence_count}个低置信度预测'
            })
        
        # 检查预测漂移
        if len(self.metrics_buffer['predictions']) >= 100:
            recent_preds = self.metrics_buffer['predictions'][-50:]
            historical_preds = self.metrics_buffer['predictions'][-100:-50]
            
            recent_mean = np.mean(recent_preds)
            historical_mean = np.mean(historical_preds)
            
            if abs(recent_mean - historical_mean) > self.alert_thresholds['prediction_drift']:
                alerts.append({
                    'type': 'prediction_drift',
                    'severity': 'high',
                    'message': f'预测分布发生漂移: {recent_mean:.3f} vs {historical_mean:.3f}'
                })
        
        return alerts
    
    def generate_dashboard_html(self):
        """生成HTML仪表板"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>电池析锂检测模型监控仪表板</title>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .metric-card { 
                    border: 1px solid #ddd; 
                    border-radius: 8px; 
                    padding: 15px; 
                    margin: 10px; 
                    display: inline-block; 
                    width: 200px; 
                }
                .alert { 
                    background-color: #ffebee; 
                    border-left: 4px solid #f44336; 
                    padding: 10px; 
                    margin: 10px 0; 
                }
            </style>
        </head>
        <body>
            <h1>🔋 电池析锂检测模型监控仪表板</h1>
            
            <div id="metrics-summary">
                <div class="metric-card">
                    <h3>总预测数</h3>
                    <h2>{total_predictions}</h2>
                </div>
                <div class="metric-card">
                    <h3>平均置信度</h3>
                    <h2>{avg_confidence:.3f}</h2>
                </div>
                <div class="metric-card">
                    <h3>析锂检出率</h3>
                    <h2>{detection_rate:.1%}</h2>
                </div>
            </div>
            
            <div id="alerts">
                {alerts_html}
            </div>
            
            <div id="prediction-timeline" style="width:100%;height:400px;"></div>
            <div id="confidence-distribution" style="width:100%;height:400px;"></div>
            
            <script>
                {plotly_scripts}
            </script>
        </body>
        </html>
        """
        
        # 计算指标
        total_predictions = len(self.metrics_buffer['predictions'])
        avg_confidence = np.mean(self.metrics_buffer['confidences']) if self.metrics_buffer['confidences'] else 0
        detection_rate = np.mean(self.metrics_buffer['predictions']) if self.metrics_buffer['predictions'] else 0
        
        # 生成警报HTML
        alerts = self._check_alerts()
        alerts_html = ""
        for alert in alerts:
            alerts_html += f'<div class="alert">{alert["message"]}</div>'
        
        # 生成Plotly脚本
        plotly_scripts = self._generate_plotly_scripts()
        
        # 填充模板
        html_content = html_template.format(
            total_predictions=total_predictions,
            avg_confidence=avg_confidence,
            detection_rate=detection_rate,
            alerts_html=alerts_html,
            plotly_scripts=plotly_scripts
        )
        
        # 保存HTML文件
        with open('monitoring_dashboard.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print("📊 监控仪表板已生成: monitoring_dashboard.html")
    
    def _generate_plotly_scripts(self):
        """生成Plotly图表脚本"""
        if not self.metrics_buffer['predictions']:
            return ""
        
        # 预测时间线图
        timeline_script = f"""
        var timeline_data = [{{
            x: {[t.isoformat() for t in self.metrics_buffer['timestamps']]},
            y: {self.metrics_buffer['predictions']},
            type: 'scatter',
            mode: 'lines+markers',
            name: '预测概率'
        }}];
        
        var timeline_layout = {{
            title: '预测时间线',
            xaxis: {{ title: '时间' }},
            yaxis: {{ title: '析锂概率' }}
        }};
        
        Plotly.newPlot('prediction-timeline', timeline_data, timeline_layout);
        """
        
        # 置信度分布图
        confidence_script = f"""
        var confidence_data = [{{
            x: {self.metrics_buffer['confidences']},
            type: 'histogram',
            nbinsx: 20,
            name: '置信度分布'
        }}];
        
        var confidence_layout = {{
            title: '置信度分布',
            xaxis: {{ title: '置信度' }},
            yaxis: {{ title: '频次' }}
        }};
        
        Plotly.newPlot('confidence-distribution', confidence_data, confidence_layout);
        """
        
        return timeline_script + confidence_script

# 使用示例
def run_comprehensive_debugging():
    """运行综合调试流程"""
    # 假设已有模型和数据
    # model = load_model('lithium_plating_detector.h5')
    # X_train, y_train, X_val, y_val = load_data()
    # feature_names = get_feature_names()
    
    # 创建调试器
    debugger = ComprehensiveDebugger(model, feature_names)
    
    # 生成调试报告
    debug_report = debugger.generate_debug_report(
        X_train, y_train, X_val, y_val
    )
    
    # 创建监控仪表板
    dashboard = RealTimeMonitoringDashboard(model, feature_names)
    dashboard.generate_dashboard_html()
    
    print("🎉 调试流程完成!")
    return debug_report

if __name__ == "__main__":
    # 运行调试
    report = run_comprehensive_debugging()
```

## 8. 调试最佳实践

### 8.1 调试检查清单

```markdown
## 🔍 电池析锂检测模型调试检查清单

### 数据层面
- [ ] 检查原始数据完整性和格式
- [ ] 验证CV阶段提取的准确性
- [ ] 确认特征提取的正确性
- [ ] 检查数据标注的一致性
- [ ] 验证训练/验证/测试集划分

### 模型层面
- [ ] 确认模型架构的合理性
- [ ] 检查损失函数的设计
- [ ] 验证物理约束的实现
- [ ] 监控训练过程的稳定性
- [ ] 评估模型的泛化能力

### 性能层面
- [ ] 分析混淆矩阵和分类报告
- [ ] 检查ROC曲线和AUC值
- [ ] 评估不同阈值下的性能
- [ ] 分析错误样本的特征
- [ ] 验证模型的鲁棒性

### 可解释性层面
- [ ] 分析特征重要性排序
- [ ] 验证物理机理的一致性
- [ ] 检查预测结果的合理性
- [ ] 生成可理解的解释报告
- [ ] 确保决策的可追溯性
```

### 8.2 调试工作流程

```python
def debugging_workflow():
    """标准调试工作流程"""
    
    print("🚀 开始电池析锂检测模型调试流程")
    
    # 第1步：数据质量检查
    print("\n📊 第1步：数据质量检查")
    data_checker = DataQualityChecker()
    # 执行数据检查...
    
    # 第2步：特征工程验证
    print("\n🔧 第2步：特征工程验证")
    feature_validator = FeatureExtractionValidator()
    # 执行特征验证...
    
    # 第3步：模型训练监控
    print("\n🏋️ 第3步：模型训练监控")
    training_monitor = TrainingMonitor()
    # 执行训练监控...
    
    # 第4步：性能评估分析
    print("\n📈 第4步：性能评估分析")
    prediction_analyzer = PredictionAnalyzer()
    # 执行性能分析...
    
    # 第5步：可解释性分析
    print("\n🧠 第5步：可解释性分析")
    explainability_analyzer = ModelExplainabilityAnalyzer(model, feature_names)
    # 执行可解释性分析...
    
    # 第6步：生成综合报告
    print("\n📄 第6步：生成综合报告")
    comprehensive_debugger = ComprehensiveDebugger(model, feature_names)
    # 生成最终报告...
    
    print("\n✅ 调试流程完成！")

# 调试配置
DEBUG_CONFIG = {
    'enable_detailed_logging': True,
    'save_intermediate_results': True,
    'generate_visualization': True,
    'export_debug_data': True,
    'real_time_monitoring': False
}
```

通过以上全面的调试指南，可以系统性地识别和解决电池析锂检测模型在开发和部署过程中遇到的各种问题，确保模型的可靠性和实用性。
